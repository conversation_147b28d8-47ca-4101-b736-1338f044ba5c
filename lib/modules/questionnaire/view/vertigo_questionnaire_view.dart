import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:gt_plus/modules/questionnaire/controller/vertigo_questionnaire_controller.dart';
import 'package:gt_plus/utils/reusableWidgets/reusable_app_bar.dart';
import 'package:gt_plus/utils/reusableWidgets/reusable_footer.dart';
import 'package:gt_plus/utils/reusableWidgets/reusable_button.dart';
import 'package:gt_plus/utils/appConst/app_colors.dart';
import 'package:gt_plus/models/questionnaire_models.dart';

class VertigoQuestionnaireView extends GetView<VertigoQuestionnaireController> {
  const VertigoQuestionnaireView({super.key});

  static const String routeName = "/VertigoQuestionnaireView";

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: reusableAppBar(context: context),
      bottomNavigationBar: const ReusableFooter(),
      body: _buildBody(context),
    );
  }

  Widget _buildBody(BuildContext context) {
    return Obx(() {
      if (controller.isInitializing.value) {
        return const Center(child: CircularProgressIndicator());
      }

      return _buildContent(context);
    });
  }

  Widget _buildContent(BuildContext context) {
    final questionnaireSet = controller.getQuestionnaireSet();
    if (questionnaireSet == null) {
      return const Center(
        child: Text('Failed to load questionnaire data'),
      );
    }

    return SingleChildScrollView(
      child: Padding(
        padding: EdgeInsets.symmetric(horizontal: context.width * .04),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const SizedBox(height: 24),
            _buildHeader(context, questionnaireSet),
            const SizedBox(height: 20),
            _buildReadOnlyIndicator(),
            const SizedBox(height: 24),
            _buildQuestions(context, questionnaireSet),
            const SizedBox(height: 32),
            _buildActionButtons(context),
            const SizedBox(height: 32),
          ],
        ),
      ),
    );
  }

  Widget _buildHeader(BuildContext context, QuestionnaireSet questionnaireSet) {
    return Text(
      questionnaireSet.label,
      style: const TextStyle(
        color: AppColors.charcoalBlue,
        fontSize: 24,
        fontWeight: FontWeight.bold,
      ),
    );
  }

  Widget _buildReadOnlyIndicator() {
    return Obx(() {
      if (!controller.isReadOnly.value) return const SizedBox.shrink();

      return Container(
        padding: const EdgeInsets.all(12),
        decoration: BoxDecoration(
          color: AppColors.softGray.withValues(alpha: 0.3),
          borderRadius: BorderRadius.circular(8),
          border: Border.all(color: AppColors.softGray),
        ),
        child: const Row(
          children: [
            Icon(Icons.info_outline, color: AppColors.charcoalBlue, size: 20),
            SizedBox(width: 8),
            Expanded(
              child: Text(
                'This questionnaire has already been completed and is in read-only mode.',
                style: TextStyle(
                  color: AppColors.charcoalBlue,
                  fontSize: 14,
                ),
              ),
            ),
          ],
        ),
      );
    });
  }

  Widget _buildQuestions(BuildContext context, QuestionnaireSet questionnaireSet) {
    if (questionnaireSet.questionnaires == null || questionnaireSet.questionnaires!.isEmpty) {
      return const SizedBox.shrink();
    }

    return Column(
      children: questionnaireSet.questionnaires!.asMap().entries.map((entry) {
        final index = entry.key;
        final questionnaire = entry.value;
        return _buildQuestion(context, questionnaire, index + 1);
      }).toList(),
    );
  }

  Widget _buildQuestion(BuildContext context, QuestionnaireQuestion questionnaire, int questionNumber) {
    return Container(
      margin: const EdgeInsets.only(bottom: 24),
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: AppColors.softGray.withValues(alpha: 0.3)),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            '$questionNumber. ${questionnaire.label}',
            style: const TextStyle(
              color: AppColors.charcoalBlue,
              fontSize: 16,
              fontWeight: FontWeight.w600,
            ),
          ),
          const SizedBox(height: 16),
          _buildQuestionOptions(context, questionnaire),
        ],
      ),
    );
  }

  Widget _buildQuestionOptions(BuildContext context, QuestionnaireQuestion questionnaire) {
    if (questionnaire.option?.type == 'radio') {
      return _buildRadioOptions(context, questionnaire);
    } else if (questionnaire.option?.type == 'checkbox') {
      return _buildCheckboxOptions(context, questionnaire);
    }
    return const SizedBox.shrink();
  }

  Widget _buildRadioOptions(BuildContext context, QuestionnaireQuestion questionnaire) {
    return Obx(() {
      return Column(
        children: questionnaire.option!.subOption!.map((option) {
          final isSelected = controller.getRadioAnswer(questionnaire.value) == option.value;
          
          return Container(
            margin: const EdgeInsets.only(bottom: 8),
            child: InkWell(
              onTap: controller.isReadOnly.value 
                  ? null 
                  : () => controller.updateRadioAnswer(questionnaire.value, option.value),
              borderRadius: BorderRadius.circular(8),
              child: Container(
                padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
                decoration: BoxDecoration(
                  color: isSelected ? AppColors.lightTeal.withValues(alpha: 0.1) : Colors.transparent,
                  borderRadius: BorderRadius.circular(8),
                  border: Border.all(
                    color: isSelected ? AppColors.lightTeal : AppColors.softGray.withValues(alpha: 0.5),
                  ),
                ),
                child: Row(
                  children: [
                    Radio<String>(
                      value: option.value,
                      groupValue: controller.getRadioAnswer(questionnaire.value),
                      onChanged: controller.isReadOnly.value 
                          ? null 
                          : (value) => controller.updateRadioAnswer(questionnaire.value, value!),
                      activeColor: AppColors.lightTeal,
                    ),
                    const SizedBox(width: 8),
                    Expanded(
                      child: Text(
                        option.label,
                        style: TextStyle(
                          color: AppColors.charcoalBlue,
                          fontSize: 14,
                          fontWeight: isSelected ? FontWeight.w500 : FontWeight.normal,
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ),
          );
        }).toList(),
      );
    });
  }

  Widget _buildCheckboxOptions(BuildContext context, QuestionnaireQuestion questionnaire) {
    return Obx(() {
      return Column(
        children: questionnaire.option!.subOption!.map((option) {
          final isSelected = controller.isCheckboxSelected(questionnaire.value, option.value);
          
          return Container(
            margin: const EdgeInsets.only(bottom: 8),
            child: InkWell(
              onTap: controller.isReadOnly.value 
                  ? null 
                  : () => controller.updateCheckboxAnswer(questionnaire.value, option.value, !isSelected),
              borderRadius: BorderRadius.circular(8),
              child: Container(
                padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
                decoration: BoxDecoration(
                  color: isSelected ? AppColors.lightTeal.withValues(alpha: 0.1) : Colors.transparent,
                  borderRadius: BorderRadius.circular(8),
                  border: Border.all(
                    color: isSelected ? AppColors.lightTeal : AppColors.softGray.withValues(alpha: 0.5),
                  ),
                ),
                child: Row(
                  children: [
                    Checkbox(
                      value: isSelected,
                      onChanged: controller.isReadOnly.value 
                          ? null 
                          : (value) => controller.updateCheckboxAnswer(questionnaire.value, option.value, value!),
                      activeColor: AppColors.lightTeal,
                    ),
                    const SizedBox(width: 8),
                    Expanded(
                      child: Text(
                        option.label,
                        style: TextStyle(
                          color: AppColors.charcoalBlue,
                          fontSize: 14,
                          fontWeight: isSelected ? FontWeight.w500 : FontWeight.normal,
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ),
          );
        }).toList(),
      );
    });
  }

  Widget _buildActionButtons(BuildContext context) {
    return Obx(() {
      if (controller.isReadOnly.value) {
        return const SizedBox.shrink();
      }

      return Row(
        children: [
          Expanded(
            child: ReusableButton(
              title: "Reset",
              onTap: controller.resetForm,
              color: Colors.grey[300],
              fontColor: AppColors.charcoalBlue,
            ),
          ),
          const SizedBox(width: 16),
          Expanded(
            child: ReusableButton(
              title: controller.isSubmitting.value ? "Submitting..." : "Submit",
              onTap: controller.isSubmitting.value ? null : controller.submitQuestionnaire,
              color: AppColors.lightTeal,
              fontColor: Colors.white,
            ),
          ),
        ],
      );
    });
  }
}
